<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <path id="circle-text-path" d="M50,10 a40,40 0 1,1 -0.1,0" fill="none" />
  </defs>
  
  <!-- Play triangle in the middle (static) -->
  <polygon points="42,35 42,65 70,50" fill="white" />
  
  <!-- Text that will rotate -->
  <text class="rotate-text" style="font-family: 'Arial', sans-serif; font-weight: bold; font-size: 10px; fill: white; text-transform: uppercase;">
    <textPath href="#circle-text-path" startOffset="5%">Watch the video • Watch the video •</textPath>
  </text>
</svg> 