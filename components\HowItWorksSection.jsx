"use client";

import { useState, useEffect, useRef } from "react";
import styles from "./HowItWorksSection.module.css";

// Content for each step
const steps = [
  {
    number: 1,
    title: "Build + Organize Your Meeting Hubs",
    description: "Define and organize the meetings most important to your organization.",
    image: "/images/howplaceholder.jpg",
    items: [
      "Team Meetings",
      "Board Meetings",
      "1-on-1s",
      "Quarterly Meetings",
      "Client Meetings"      
    ]
  },
  {
    number: 2,
    title: "Invite Your Team + Clients",
    description: "Bring internal and external stakeholders into the appropriate meeting hubs.",
    image: "/images/howplaceholder2.jpg",
    items: [
      "Orgs, Teams, and Individual Settings",
      "Permission Settings",
      "User Directory",
      "Collaboration Tools"
    ]
  },
  {
    number: 3,
    title: "Customize Your Hubs to Fit Your Needs",
    description: "Make meetings work for you! Create hubs that help you get the most out of your recurring meetings.",
    image: "/images/howplaceholder.jpg",
    items: [
      "Define Goals + KPIs",
      "Meeting Tool Integrations",
      "Customize Agendas",
      "Accountability Tracking",
      "Choose Meeting Templates" 
    ]
  },
  {
    number: 4,
    title: "Run Effective + account-able Meetings",
    description: "Collaborate in an interactive environment that keeps meetings focused and purposeful.",
    image: "/images/howplaceholder2.jpg",
    items: [
      "Automated Previews + Recaps",
      "AI Notetaker Integration",
      "Meeting Feedback",
      "In-App Timers"      
    ]
  }
];

export default function HowItWorksSection() {
  const [activeStep, setActiveStep] = useState(0);
  const [autoAdvance, setAutoAdvance] = useState(true);
  const [isMobile, setIsMobile] = useState(false);
  const [mobileActiveStep, setMobileActiveStep] = useState(0); // Set Step 1 active by default
  const timerRef = useRef(null);
  const progressRef = useRef(null);

  // Check if mobile
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };
    
    checkIfMobile();
    window.addEventListener('resize', checkIfMobile);
    
    return () => {
      window.removeEventListener('resize', checkIfMobile);
    };
  }, []);

  // Auto-advance timer (only for desktop)
  useEffect(() => {
    if (autoAdvance && !isMobile) {
      // Reset progress bar animation
      if (progressRef.current) {
        progressRef.current.style.transition = 'none';
        progressRef.current.style.width = '0%';
        // Force reflow
        progressRef.current.offsetHeight;
        progressRef.current.style.transition = 'width 10s linear';
        progressRef.current.style.width = '100%';
      }

      // Set timer for next slide
      timerRef.current = setTimeout(() => {
        setActiveStep((prev) => (prev + 1) % steps.length);
      }, 10000); // 10 seconds per slide
    }

    return () => {
      if (timerRef.current) clearTimeout(timerRef.current);
    };
  }, [activeStep, autoAdvance, isMobile]);

  const handleStepClick = (index) => {
    if (isMobile) {
      setMobileActiveStep(mobileActiveStep === index ? null : index);
    } else {
      setActiveStep(index);
      setAutoAdvance(false); // Stop auto-advance when user clicks
      
      // Restart auto-advance after 30 seconds of inactivity
      setTimeout(() => {
        setAutoAdvance(true);
      }, 30000);
    }
  };

  const handlePrevClick = () => {
    setActiveStep((prev) => (prev - 1 + steps.length) % steps.length);
    setAutoAdvance(false);
  };

  const handleNextClick = () => {
    setActiveStep((prev) => (prev + 1) % steps.length);
    setAutoAdvance(false);
  };

  // Mobile accordion version
  if (isMobile) {
    return (
      <section className={styles.howItWorksSection}>
        <div className={styles.howItWorksContainer}>
          {/* Header */}
          <div className={styles.howItWorksHeader}>
            <span className={styles.howItWorksLabel}>How It Works</span>
            <h2 className={styles.howItWorksTitle}>
              Get Started for Free and Start Running Better Meetings
            </h2>
          </div>

          {/* Mobile Accordion */}
          <div className={styles.mobileAccordion}>
            {steps.map((step, index) => (
              <div 
                key={index} 
                className={`${styles.mobileAccordionItem} ${mobileActiveStep === index ? styles.mobileAccordionItemActive : ''}`}
              >
                <div 
                  className={styles.mobileAccordionHeader}
                  onClick={() => handleStepClick(index)}
                >
                  <span className={styles.mobileStepNumber}>Step {step.number}</span>
                  <span className={styles.mobileStepTitle}>{step.title}</span>
                  <span className={styles.mobileAccordionIcon}>
                    <img 
                      src={mobileActiveStep === index ? "/up-arrow.svg" : "/down-arrow.svg"} 
                      alt={mobileActiveStep === index ? "Collapse" : "Expand"}
                      width="16"
                      height="16"
                    />
                  </span>
                </div>
                
                {mobileActiveStep === index && (
                  <div className={styles.mobileAccordionContent}>
                    <div className={styles.mobileImageContainer}>
                      <img 
                        src={step.image} 
                        alt={`Step ${step.number}: ${step.title}`} 
                        className={styles.mobileStepImage} 
                      />
                    </div>
                    
                    <h3 className={styles.mobileStepDetailsTitle}>{step.title}</h3>
                    <p className={styles.mobileStepDescription}>{step.description}</p>
                    
                    <div className={styles.mobileItemsList}>
                      {step.items.map((item, idx) => (
                        <div key={idx} className={styles.mobileItem}>
                          <span className={styles.mobileBulletPoint}></span>
                          <span>{item}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  // Desktop version
  return (
    <section className={styles.howItWorksSection}>
      <div className={styles.howItWorksContainer}>
        {/* Header */}
        <div className={styles.howItWorksHeader}>
          <span className={styles.howItWorksLabel}>How It Works</span>
          <h2 className={styles.howItWorksTitle}>
            Get Started for Free and Start Running Better Meetings
          </h2>
        </div>

        {/* Main container with fixed size */}
        <div className={styles.mainContainer}>
          {/* Steps Navigation */}
          <div className={styles.stepsNav}>
            {steps.map((step, index) => (
              <div 
                key={index} 
                className={`${styles.stepItem} ${activeStep === index ? styles.stepItemActive : ''}`}
                onClick={() => handleStepClick(index)}
              >
                {activeStep === index && (
                  <div className={styles.progressBarContainer}>
                    <div 
                      ref={index === activeStep ? progressRef : null}
                      className={styles.progressBar}
                    ></div>
                  </div>
                )}
                <span className={styles.stepNumber}>Step {step.number}</span>
                <span className={styles.stepText}>{step.title}</span>
              </div>
            ))}
          </div>

          {/* Content Area */}
          <div className={styles.stepsContent}>
            {/* Image container - left side */}
            <div className={styles.imageContainer}>
              {steps.map((step, index) => (
                <div 
                  key={index} 
                  className={styles.imageWrapper}
                  style={{ 
                    display: activeStep === index ? 'block' : 'none'
                  }}
                >
                  <img 
                    src={step.image} 
                    alt={`Step ${step.number}: ${step.title}`} 
                    className={styles.stepImage} 
                  />
                </div>
              ))}
            </div>

            {/* Step details - right side */}
            <div className={styles.stepDetails}>
              <span className={styles.stepBadge}>Step {steps[activeStep].number}</span>
              <h3 className={styles.stepDetailsTitle}>
                {steps[activeStep].title}
              </h3>
              <p className={styles.stepDetailsText}>
                {steps[activeStep].description}
              </p>
              
              {/* Checkboxes */}
              <div className={styles.checkboxList}>
                {steps[activeStep].items.map((item, index) => (
                  <div key={index} className={styles.checkboxItem}>
                    <label>{item}</label>
                  </div>
                ))}
              </div>

              {/* Navigation buttons */}
              <div className={styles.navigationButtons}>
                <button 
                  className={styles.navButton} 
                  onClick={handlePrevClick}
                  aria-label="Previous step"
                >
                  <img src="/leftnav.svg" alt="Previous" />
                </button>
                <button 
                  className={styles.navButton} 
                  onClick={handleNextClick}
                  aria-label="Next step"
                >
                  <img src="/rightnav.svg" alt="Next" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
} 