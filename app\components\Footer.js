"use client";

import { usePathname } from "next/navigation";
import styles from "../layout.module.css";

export default function Footer() {
  const pathname = usePathname();
  
  // Check if current path is the contact page
  const isContactPage = pathname === "/contact";

  if (isContactPage) {
    return null; // Don't render footer on contact page
  }

  return (
    <>
      {/* Footer / CTA Section */}
      <div className={styles.footerWrapper}>
        <footer className={styles.footerSection}>
          <div className={styles.footerInner}>
            <h2 className={styles.footerHeading}>
              Transform Your Meetings Today
            </h2>
            <p className={styles.footerText}>
              Discover how our app can streamline your meetings and enhance
              team collaboration effortlessly.
            </p>
            <a href="/contact" className={styles.footerButton}>Get a Demo</a>

            <div className={styles.footerImageWrapper}>
              <img
                src="/images/artboard-background.png"
                alt="App Screenshot"
                className={styles.footerImage}
              />
            </div>
          </div>
        </footer>
      </div>
      
      {/* Footer Bar - Separate from the main footer wrapper */}
      <div className={styles.footerBar}>
        <div className={styles.footerLinks}>
          <a href="/privacy-policy" className={styles.footerLink}>
            Privacy Policy
          </a>
          <span className={styles.footerCopy}>© Accountable 2025</span>
        </div>
        <div className={styles.socialIconsContainer}>
          <a
            href="https://www.linkedin.com/your-page"
            target="_blank"
            rel="noopener noreferrer"
          >
            <img
              src="/linkedin.svg"
              alt="LinkedIn"
              className={styles.socialIcon}
            />
          </a>
          <a
            href="https://www.youtube.com/your-channel"
            target="_blank"
            rel="noopener noreferrer"
          >
            <img
              src="/youtube.svg"
              alt="YouTube"
              className={styles.socialIcon}
            />
          </a>
        </div>
      </div>
    </>
  );
}
