/* Contact page styles */
.pageWrapper {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.mainContent {
  flex: 1;
  width: 100%;
  background-color: #F6F9FB;
  border-radius: 0 0 75px 75px;
  position: relative;
  z-index: 1;
  margin-bottom: -75px;
}

.stickyFooter {
  margin-top: auto;
}

/* Top logo styles */
.topLogoContainer {
  padding: 72px 20px 0 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.logoLink {
  display: inline-block;
  text-decoration: none;
}

.topLogo {
  max-width: 214px;
  height: auto;
}

.contactSection {
  padding: 40px 20px 150px;
  max-width: 1200px;
  margin: 0 auto;
}

.contactContainer {
  display: flex;
  gap: 100px;
}

.contactLeft {
  flex: 1;
  max-width: 500px;
  align-content: center;
}

.teamImage {
  max-width: 170px;
}

.contactTitle {
  font-size: 47px;
  font-weight: 600;
  color: #44505C;
  margin-bottom: 24px;
  line-height: 1.2;
  margin-top: 20px;
}

.contactDescription {
  font-size: 20px;
  line-height: 1.6;
  color: #44505C;
  margin-bottom: 32px;
}

.benefitsList {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.benefitItem {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.checkIcon {
  width: 27px;
  height: 27px;
  margin-top: 3px;
}

.benefitItem p {
  color: #44505C;
  font-size: 20px;
  line-height: 1.5;
  margin: 0;
}

.contactRight {
  flex: 1;
}

.formContainer {
  background-color: #30C8AF;
  border-radius: 16px;
  padding: 90px 32px 32px 32px;
  position: relative;
  color: #fff;
}

.contactForm {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.formRow {
  display: flex;
  gap: 10px;
}

.formGroup {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.formGroup label {
  font-size: 13px;
  font-weight: 400;
}

.formGroup input,
.formGroup select {
  height: 48px;
  border-radius: 6px;
  border: none;
  padding: 0 16px;
  font-size: 15px;
  color: #44505C;
  width: 100%;
  box-sizing: border-box;
  background-color: #fff;
  outline: none;
  font-family: 'sfCompact', sans-serif;
}

.formGroup select {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-image: url("data:image/svg+xml;utf8,<svg width='12' height='8' viewBox='0 0 12 8' fill='none' xmlns='http://www.w3.org/2000/svg'><path d='M1 1.5L6 6.5L11 1.5' stroke='%2344505C' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/></svg>");
  background-repeat: no-repeat;
  background-position: calc(100% - 10px) center;
  padding-right: 30px;
}

.phoneInputGroup {
  display: flex;
  width: 100%;
  position: relative;
}

.countrySelector {
  height: 48px;
  line-height: 48px;
  border: none;
  border-radius: 6px 6px 6px 6px;
  padding: 0 12px;
  font-size: 15px;
  color: #44505C;
  background-color: #fff;
  margin-right: 10px;
  width: 25%;
  flex-shrink: 0;
  display: inline-block;
  align-items: center;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-right: 30px;
  position: relative;
}

.countrySelector span {
  flex-shrink: 0;
  margin-left: 4px;
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
}

.dropdownArrow {
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
}

.countryDropdown {
  position: absolute;
  top: 48px;
  left: 0;
  width: 100%;
  max-height: 300px;
  background-color: #fff;
  border-radius: 0 0 6px 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 10;
  overflow: hidden;
}

.countrySearchInput {
  width: 100%;
  padding: 10px 12px;
  border: none;
  border-bottom: 1px solid #E2E8F0;
  font-size: 14px;
  outline: none;
}

.countryList {
  max-height: 250px;
  overflow-y: auto;
}

.countryOption {
  padding: 10px 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  color: #44505C;
}

.countryOption:hover {
  background-color: #E2EEF5;
}

.selectedCountry {
  background-color: #1e88e5;
  color: #fff;
}

.phoneInput {
  border-radius: 0 6px 6px 0;
  flex-grow: 1;
  width: 70%;
}

.countryCodeSelect {
  display: none;
}

.submitButton {
  background-color: #FF5C6C;
  color: #fff;
  border: 1px solid #FFF;
  border-radius: 6px 6px 20px 6px;
  height: 56px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  margin-top: 16px;
  width: fit-content;
  padding: 20px 36px 20px 36px;
  transition: all 0.3s ease;
}

.submitButton:hover {
  background-color: #fff;
  color: #FF5C6C;
  border: 1px solid #FF5C6C;
  box-shadow: none;
}

.formDisclaimer {
  font-size: 12px;
  line-height: 1.5;
  margin-top: 16px;
  text-align: left;
  font-weight: 400;
  width: 75%;
}

.logoSection {
  background-color: #161616;
  padding: 170px 0 116px 0;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  z-index: 0;
  padding-top: 195px;
}

.contactLogo {
  max-width: 1167px;
  height: auto;
}

/* Footer Bar */
.footerBar {
  background-color: #000;
  padding: 16px 100px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
}

.footerLinks {
  display: flex;
  justify-content: flex-start;
  gap: 32px;
  align-self: center;
}

.footerLink,
.footerCopy {
  color: rgb(255, 255, 255);
  font-size: 14px;
  line-height: 1.4;
  font-weight: 300;
}

.socialIconsContainer {
  display: flex;
  gap: 16px;
  display: none;
}

.socialIcon {
  width: 42px;
  height: 42px;
}

/* Responsive styles */
@media (max-width: 991px) {
  .contactContainer {
    flex-direction: column;
  }
  
  .contactLeft {
    max-width: 100%;
  }
  
  .footerBar {
    padding: 16px 20px;
    display: flex;
    flex-direction: row;
  }
  
  .footerLinks {
    justify-content: flex-start;
    align-self: center;
    gap: 16px;
    display: flex;
    flex-direction: row;
  }
  
  .socialIconsContainer {
    gap: 8px;
  }
}

@media (max-width: 768px) {
  .formRow {
    flex-direction: column;
    gap: 16px;
  }
  
  .formContainer {
    padding: 30px 20px;
  }
  
  .contactTitle {
    font-size: 32px;
  }
  
  .contactLogo {
    max-width: 300px;
  }
}

.statusMessage {
  margin-top: 15px;
  padding: 10px 15px;
  border-radius: 4px;
  text-align: center;
  font-size: 14px;
}

.success {
  background-color: #e6f7e6;
  color: #0d6832;
  border: 1px solid #b8e5b8;
}

.error {
  background-color: #fce8e8;
  color: #d32f2f;
  border: 1px solid #f5c2c2;
}



