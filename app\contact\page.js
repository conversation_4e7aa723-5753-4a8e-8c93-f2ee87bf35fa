"use client";
import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import styles from './contact.module.css';
import { useGoogleReCaptcha } from 'react-google-recaptcha-v3';
import RecaptchaProvider from '../components/RecaptchaProvider';

// Country data with codes for the phone dropdown
const countries = [
  { name: "Afghanistan", code: "AF", dialCode: "+93" },
  { name: "Albania", code: "AL", dialCode: "+355" },
  { name: "Algeria", code: "DZ", dialCode: "+213" },
  { name: "Andorra", code: "AD", dialCode: "+376" },
  { name: "Angola", code: "AO", dialCode: "+244" },
  { name: "Argentina", code: "AR", dialCode: "+54" },
  { name: "Armenia", code: "AM", dialCode: "+374" },
  { name: "Australia", code: "AU", dialCode: "+61" },
  { name: "Austria", code: "AT", dialCode: "+43" },
  { name: "Azerbaijan", code: "AZ", dialCode: "+994" },
  { name: "Bahamas", code: "BS", dialCode: "******" },
  { name: "Bahrain", code: "BH", dialCode: "+973" },
  { name: "Bangladesh", code: "BD", dialCode: "+880" },
  { name: "Barbados", code: "BB", dialCode: "******" },
  { name: "Belarus", code: "BY", dialCode: "+375" },
  { name: "Belgium", code: "BE", dialCode: "+32" },
  { name: "Belize", code: "BZ", dialCode: "+501" },
  { name: "Benin", code: "BJ", dialCode: "+229" },
  { name: "Bhutan", code: "BT", dialCode: "+975" },
  { name: "Bolivia", code: "BO", dialCode: "+591" },
  { name: "Bosnia and Herzegovina", code: "BA", dialCode: "+387" },
  { name: "Botswana", code: "BW", dialCode: "+267" },
  { name: "Brazil", code: "BR", dialCode: "+55" },
  { name: "Brunei", code: "BN", dialCode: "+673" },
  { name: "Bulgaria", code: "BG", dialCode: "+359" },
  { name: "Burkina Faso", code: "BF", dialCode: "+226" },
  { name: "Burundi", code: "BI", dialCode: "+257" },
  { name: "Cambodia", code: "KH", dialCode: "+855" },
  { name: "Cameroon", code: "CM", dialCode: "+237" },
  { name: "Canada", code: "CA", dialCode: "+1" },
  { name: "Cape Verde", code: "CV", dialCode: "+238" },
  { name: "Central African Republic", code: "CF", dialCode: "+236" },
  { name: "Chad", code: "TD", dialCode: "+235" },
  { name: "Chile", code: "CL", dialCode: "+56" },
  { name: "China", code: "CN", dialCode: "+86" },
  { name: "Colombia", code: "CO", dialCode: "+57" },
  { name: "Comoros", code: "KM", dialCode: "+269" },
  { name: "Costa Rica", code: "CR", dialCode: "+506" },
  { name: "Croatia", code: "HR", dialCode: "+385" },
  { name: "Cuba", code: "CU", dialCode: "+53" },
  { name: "Cyprus", code: "CY", dialCode: "+357" },
  { name: "Czech Republic", code: "CZ", dialCode: "+420" },
  { name: "Denmark", code: "DK", dialCode: "+45" },
  { name: "Djibouti", code: "DJ", dialCode: "+253" },
  { name: "Dominican Republic", code: "DO", dialCode: "+1-809" },
  { name: "Ecuador", code: "EC", dialCode: "+593" },
  { name: "Egypt", code: "EG", dialCode: "+20" },
  { name: "El Salvador", code: "SV", dialCode: "+503" },
  { name: "Estonia", code: "EE", dialCode: "+372" },
  { name: "Ethiopia", code: "ET", dialCode: "+251" },
  { name: "Fiji", code: "FJ", dialCode: "+679" },
  { name: "Finland", code: "FI", dialCode: "+358" },
  { name: "France", code: "FR", dialCode: "+33" },
  { name: "Gabon", code: "GA", dialCode: "+241" },
  { name: "Gambia", code: "GM", dialCode: "+220" },
  { name: "Georgia", code: "GE", dialCode: "+995" },
  { name: "Germany", code: "DE", dialCode: "+49" },
  { name: "Ghana", code: "GH", dialCode: "+233" },
  { name: "Greece", code: "GR", dialCode: "+30" },
  { name: "Guatemala", code: "GT", dialCode: "+502" },
  { name: "Guinea", code: "GN", dialCode: "+224" },
  { name: "Haiti", code: "HT", dialCode: "+509" },
  { name: "Honduras", code: "HN", dialCode: "+504" },
  { name: "Hong Kong", code: "HK", dialCode: "+852" },
  { name: "Hungary", code: "HU", dialCode: "+36" },
  { name: "Iceland", code: "IS", dialCode: "+354" },
  { name: "India", code: "IN", dialCode: "+91" },
  { name: "Indonesia", code: "ID", dialCode: "+62" },
  { name: "Iran", code: "IR", dialCode: "+98" },
  { name: "Iraq", code: "IQ", dialCode: "+964" },
  { name: "Ireland", code: "IE", dialCode: "+353" },
  { name: "Israel", code: "IL", dialCode: "+972" },
  { name: "Italy", code: "IT", dialCode: "+39" },
  { name: "Jamaica", code: "JM", dialCode: "+1-876" },
  { name: "Japan", code: "JP", dialCode: "+81" },
  { name: "Jordan", code: "JO", dialCode: "+962" },
  { name: "Kazakhstan", code: "KZ", dialCode: "+7" },
  { name: "Kenya", code: "KE", dialCode: "+254" },
  { name: "Kuwait", code: "KW", dialCode: "+965" },
  { name: "Kyrgyzstan", code: "KG", dialCode: "+996" },
  { name: "Latvia", code: "LV", dialCode: "+371" },
  { name: "Lebanon", code: "LB", dialCode: "+961" },
  { name: "Lesotho", code: "LS", dialCode: "+266" },
  { name: "Liberia", code: "LR", dialCode: "+231" },
  { name: "Libya", code: "LY", dialCode: "+218" },
  { name: "Liechtenstein", code: "LI", dialCode: "+423" },
  { name: "Lithuania", code: "LT", dialCode: "+370" },
  { name: "Luxembourg", code: "LU", dialCode: "+352" },
  { name: "Madagascar", code: "MG", dialCode: "+261" },
  { name: "Malawi", code: "MW", dialCode: "+265" },
  { name: "Malaysia", code: "MY", dialCode: "+60" },
  { name: "Maldives", code: "MV", dialCode: "+960" },
  { name: "Mali", code: "ML", dialCode: "+223" },
  { name: "Malta", code: "MT", dialCode: "+356" },
  { name: "Mauritania", code: "MR", dialCode: "+222" },
  { name: "Mauritius", code: "MU", dialCode: "+230" },
  { name: "Mexico", code: "MX", dialCode: "+52" },
  { name: "Monaco", code: "MC", dialCode: "+377" },
  { name: "Mongolia", code: "MN", dialCode: "+976" },
  { name: "Montenegro", code: "ME", dialCode: "+382" },
  { name: "Morocco", code: "MA", dialCode: "+212" },
  { name: "Mozambique", code: "MZ", dialCode: "+258" },
  { name: "Myanmar", code: "MM", dialCode: "+95" },
  { name: "Namibia", code: "NA", dialCode: "+264" },
  { name: "Nepal", code: "NP", dialCode: "+977" },
  { name: "Netherlands", code: "NL", dialCode: "+31" },
  { name: "New Zealand", code: "NZ", dialCode: "+64" },
  { name: "Nicaragua", code: "NI", dialCode: "+505" },
  { name: "Niger", code: "NE", dialCode: "+227" },
  { name: "Nigeria", code: "NG", dialCode: "+234" },
  { name: "North Korea", code: "KP", dialCode: "+850" },
  { name: "Norway", code: "NO", dialCode: "+47" },
  { name: "Oman", code: "OM", dialCode: "+968" },
  { name: "Pakistan", code: "PK", dialCode: "+92" },
  { name: "Panama", code: "PA", dialCode: "+507" },
  { name: "Papua New Guinea", code: "PG", dialCode: "+675" },
  { name: "Paraguay", code: "PY", dialCode: "+595" },
  { name: "Peru", code: "PE", dialCode: "+51" },
  { name: "Philippines", code: "PH", dialCode: "+63" },
  { name: "Poland", code: "PL", dialCode: "+48" },
  { name: "Portugal", code: "PT", dialCode: "+351" },
  { name: "Puerto Rico", code: "PR", dialCode: "+1-787" },
  { name: "Qatar", code: "QA", dialCode: "+974" },
  { name: "Réunion", code: "RE", dialCode: "+262" },
  { name: "Romania", code: "RO", dialCode: "+40" },
  { name: "Russia", code: "RU", dialCode: "+7" },
  { name: "Rwanda", code: "RW", dialCode: "+250" },
  { name: "Saint Barthélemy", code: "BL", dialCode: "+590" },
  { name: "Saint Helena", code: "SH", dialCode: "+290" },
  { name: "Saint Kitts and Nevis", code: "KN", dialCode: "+1-869" },
  { name: "Saint Lucia", code: "LC", dialCode: "+1-758" },
  { name: "Saint Martin", code: "MF", dialCode: "+590" },
  { name: "Saint Pierre and Miquelon", code: "PM", dialCode: "+508" },
  { name: "Saint Vincent and the Grenadines", code: "VC", dialCode: "+1-784" },
  { name: "Samoa", code: "WS", dialCode: "+685" },
  { name: "San Marino", code: "SM", dialCode: "+378" },
  { name: "São Tomé and Príncipe", code: "ST", dialCode: "+239" },
  { name: "Saudi Arabia", code: "SA", dialCode: "+966" },
  { name: "Senegal", code: "SN", dialCode: "+221" },
  { name: "Serbia", code: "RS", dialCode: "+381" },
  { name: "Sierra Leone", code: "SL", dialCode: "+232" },
  { name: "Singapore", code: "SG", dialCode: "+65" },
  { name: "Slovakia", code: "SK", dialCode: "+421" },
  { name: "Slovenia", code: "SI", dialCode: "+386" },
  { name: "Somalia", code: "SO", dialCode: "+252" },
  { name: "South Africa", code: "ZA", dialCode: "+27" },
  { name: "South Korea", code: "KR", dialCode: "+82" },
  { name: "Spain", code: "ES", dialCode: "+34" },
  { name: "Sri Lanka", code: "LK", dialCode: "+94" },
  { name: "Sudan", code: "SD", dialCode: "+249" },
  { name: "Sweden", code: "SE", dialCode: "+46" },
  { name: "Switzerland", code: "CH", dialCode: "+41" },
  { name: "Syria", code: "SY", dialCode: "+963" },
  { name: "Taiwan", code: "TW", dialCode: "+886" },
  { name: "Tajikistan", code: "TJ", dialCode: "+992" },
  { name: "Tanzania", code: "TZ", dialCode: "+255" },
  { name: "Thailand", code: "TH", dialCode: "+66" },
  { name: "Togo", code: "TG", dialCode: "+228" },
  { name: "Trinidad and Tobago", code: "TT", dialCode: "+1-868" },
  { name: "Tunisia", code: "TN", dialCode: "+216" },
  { name: "Turkey", code: "TR", dialCode: "+90" },
  { name: "Turkmenistan", code: "TM", dialCode: "+993" },
  { name: "Uganda", code: "UG", dialCode: "+256" },
  { name: "Ukraine", code: "UA", dialCode: "+380" },
  { name: "United Arab Emirates", code: "AE", dialCode: "+971" },
  { name: "United Kingdom", code: "GB", dialCode: "+44" },
  { name: "United States", code: "US", dialCode: "+1" },
  { name: "Uruguay", code: "UY", dialCode: "+598" },
  { name: "Uzbekistan", code: "UZ", dialCode: "+998" },
  { name: "Venezuela", code: "VE", dialCode: "+58" },
  { name: "Vietnam", code: "VN", dialCode: "+84" },
  { name: "Yemen", code: "YE", dialCode: "+967" },
  { name: "Zambia", code: "ZM", dialCode: "+260" },
  { name: "Zimbabwe", code: "ZW", dialCode: "+263" }
];

// Wrap the Contact component with RecaptchaProvider
function ContactWithRecaptcha() {
  return (
    <RecaptchaProvider>
      <Contact />
    </RecaptchaProvider>
  );
}

function Contact() {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phoneCountry: '',
    phone: '',
    role: '',
    country: '',
    annualRevenue: '',
    employees: ''
  });

  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredCountries, setFilteredCountries] = useState(countries);

  const dropdownRef = useRef(null);
  const { executeRecaptcha } = useGoogleReCaptcha();

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState(null);

  useEffect(() => {
    // Detect user's location based on IP address
    fetch('https://ipapi.co/json/')
      .then(response => response.json())
      .then(data => {
        const countryCode = data.country;
        const country = countries.find(c => c.code === countryCode);
        if (country) {
          setFormData(prev => ({
            ...prev,
            phoneCountry: country.code,
            country: country.code,
            phone: country.dialCode + ' '
          }));
        }
      })
      .catch(error => {
        console.error('Error fetching location:', error);
      });
  }, []);

  useEffect(() => {
    // Function to handle clicks outside the dropdown
    function handleClickOutside(event) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setDropdownOpen(false);
      }
    }

    // Add event listener when dropdown is open
    if (dropdownOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    // Clean up the event listener
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [dropdownOpen]);

  useEffect(() => {
    if (searchTerm) {
      const filtered = countries.filter(country => 
        country.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredCountries(filtered);
    } else {
      setFilteredCountries(countries);
    }
  }, [searchTerm]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const handlePhoneCountrySearch = (e) => {
    setSearchTerm(e.target.value);
  };

  const selectCountry = (country) => {
    setFormData({
      ...formData,
      phoneCountry: country.code,
      phone: country.dialCode + ' '
    });
    setDropdownOpen(false);
    setSearchTerm('');
  };

  const toggleDropdown = () => {
    setDropdownOpen(!dropdownOpen);
    setSearchTerm('');
    setFilteredCountries(countries);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitStatus(null);
    
    try {
      // Execute reCAPTCHA
      if (!executeRecaptcha) {
        console.error('Execute recaptcha not available');
        setSubmitStatus({ type: 'error', message: 'reCAPTCHA validation failed. Please try again.' });
        setIsSubmitting(false);
        return;
      }
      
      const recaptchaToken = await executeRecaptcha('contact_form');
      
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          recaptchaToken
        }),
      });
      
      const data = await response.json();
      
      if (data.success) {
        setSubmitStatus({ type: 'success', message: 'Thank you! We will contact you shortly.' });
        // Reset form after successful submission
        setFormData({
          firstName: '',
          lastName: '',
          email: '',
          phoneCountry: '',
          phone: '',
          role: '',
          country: '',
          annualRevenue: '',
          employees: ''
        });
      } else {
        setSubmitStatus({ type: 'error', message: data.message || 'Something went wrong. Please try again later.' });
      }
    } catch (error) {
      console.error('Error submitting form:', error);
      setSubmitStatus({ type: 'error', message: 'Something went wrong. Please try again later.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Find the selected country for display
  const selectedCountry = countries.find(c => c.code === formData.phoneCountry);

  return (
    <div className={styles.pageWrapper}>
      <main className={styles.mainContent}>
        <div className={styles.contactPage}>
          <div className={styles.topLogoContainer}>
            <Link href="/" className={styles.logoLink}>
              <img
                src="/contact-logo.svg"
                alt="account-able logo"
                className={styles.topLogo}
              />
            </Link>
          </div>
          
          <section className={styles.contactSection}>
            <div className={styles.contactContainer}>
              <div className={styles.contactLeft}>
                <img 
                  src="/images/Group 4441.png" 
                  alt="Team members" 
                  className={styles.teamImage} 
                />
                <h1 className={styles.contactTitle}>Book a Demo</h1>
                <p className={styles.contactDescription}>
                  Book a demo with our team to explore how account-able can streamline your 
                  meeting management and collaboration processes.
                </p>
                <div className={styles.benefitsList}>
                  <div className={styles.benefitItem}>
                    <img src="/checkmark.svg" alt="Checkmark" className={styles.checkIcon} />
                    <p>Get your questions answered by experts in meeting management and collaboration</p>
                  </div>
                  <div className={styles.benefitItem}>
                    <img src="/checkmark.svg" alt="Checkmark" className={styles.checkIcon} />
                    <p>See how account-able helps you maintain optimal meeting efficiency with real-time updates</p>
                  </div>
                  <div className={styles.benefitItem}>
                    <img src="/checkmark.svg" alt="Checkmark" className={styles.checkIcon} />
                    <p>Learn about the implementation process and next steps for getting started with account-able</p>
                  </div>
                </div>
              </div>

              <div className={styles.contactRight}>
                <div className={styles.formContainer}>
                  <form onSubmit={handleSubmit} className={styles.contactForm}>
                    <div className={styles.formRow}>
                      <div className={styles.formGroup}>
                        <label htmlFor="firstName">First Name *</label>
                        <input
                          type="text"
                          id="firstName"
                          name="firstName"
                          value={formData.firstName}
                          onChange={handleChange}
                          required
                        />
                      </div>
                      <div className={styles.formGroup}>
                        <label htmlFor="lastName">Last Name</label>
                        <input
                          type="text"
                          id="lastName"
                          name="lastName"
                          value={formData.lastName}
                          onChange={handleChange}
                        />
                      </div>
                    </div>

                    <div className={styles.formRow}>
                      <div className={styles.formGroup}>
                        <label htmlFor="email">Email *</label>
                        <input
                          type="email"
                          id="email"
                          name="email"
                          value={formData.email}
                          onChange={handleChange}
                          required
                        />
                      </div>
                      <div className={styles.formGroup}>
                        <label htmlFor="phone">Phone Number</label>
                        <div className={styles.phoneInputGroup}>
                          <div className={styles.countrySelector} onClick={toggleDropdown}>
                            {selectedCountry ? selectedCountry.name : 'Select'}
                            <span className={styles.dropdownArrow}>
                              <svg width="12" height="8" viewBox="0 0 12 8" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M1 1.5L6 6.5L11 1.5" stroke="#44505C" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                              </svg>
                            </span>
                          </div>
                          {dropdownOpen && (
                            <div className={styles.countryDropdown} ref={dropdownRef}>
                              <input
                                type="text"
                                placeholder="Search..."
                                value={searchTerm}
                                onChange={handlePhoneCountrySearch}
                                className={styles.countrySearchInput}
                                onClick={(e) => e.stopPropagation()}
                              />
                              <div className={styles.countryList}>
                                {filteredCountries.map((country) => (
                                  <div 
                                    key={country.code} 
                                    className={`${styles.countryOption} ${formData.phoneCountry === country.code ? styles.selectedCountry : ''}`}
                                    onClick={() => selectCountry(country)}
                                  >
                                    {country.name}
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                          <input
                            type="tel"
                            id="phone"
                            name="phone"
                            value={formData.phone}
                            onChange={handleChange}
                            placeholder="Phone number"
                            className={styles.phoneInput}
                          />
                        </div>
                      </div>
                    </div>

                    <div className={styles.formRow}>
                      <div className={styles.formGroup}>
                        <label htmlFor="role">Role</label>
                        <select
                          id="role"
                          name="role"
                          value={formData.role}
                          onChange={handleChange}
                        >
                          <option value="">Please Select</option>
                          <option value="manager">Manager</option>
                          <option value="director">Director</option>
                          <option value="executive">Executive</option>
                          <option value="other">Other</option>
                        </select>
                      </div>
                      <div className={styles.formGroup}>
                        <label htmlFor="country">Country</label>
                        <select
                          id="country"
                          name="country"
                          value={formData.country}
                          onChange={handleChange}
                        >
                          <option value="">Please Select</option>
                          {countries.map((country) => (
                            <option key={country.code} value={country.code}>
                              {country.name}
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>

                    <div className={styles.formRow}>
                      <div className={styles.formGroup}>
                        <label htmlFor="annualRevenue">Annual Revenue</label>
                        <select
                          id="annualRevenue"
                          name="annualRevenue"
                          value={formData.annualRevenue}
                          onChange={handleChange}
                        >
                          <option value="">Please Select</option>
                          <option value="less1m">Less than $1M</option>
                          <option value="1m-5m">$1M - $5M</option>
                          <option value="5m-10m">$5M - $10M</option>
                          <option value="more10m">More than $10M</option>
                        </select>
                      </div>
                      <div className={styles.formGroup}>
                        <label htmlFor="employees">Number of Employees</label>
                        <select
                          id="employees"
                          name="employees"
                          value={formData.employees}
                          onChange={handleChange}
                        >
                          <option value="">Please Select</option>
                          <option value="1-10">1-10</option>
                          <option value="11-50">11-50</option>
                          <option value="51-200">51-200</option>
                          <option value="201+">201+</option>
                        </select>
                      </div>
                    </div>

                    <button 
                      type="submit" 
                      className={styles.submitButton}
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? 'Sending...' : 'Book a Demo Now'}
                    </button>

                    {submitStatus && (
                      <div className={`${styles.statusMessage} ${styles[submitStatus.type]}`}>
                        {submitStatus.message}
                      </div>
                    )}

                    <p className={styles.formDisclaimer}>
                      Book a demo with our team to explore how account-able can streamline your 
                      meeting management and collaboration processes.
                    </p>
                  </form>
                </div>
              </div>
            </div>
          </section>
        </div>
      </main>

      <footer className={styles.stickyFooter}>
        <section className={styles.logoSection}>
          <img 
            src="/contact-logo.svg" 
            alt="account-able" 
            className={styles.contactLogo} 
          />
        </section>

        <div className={styles.footerBar}>
          <div className={styles.footerLinks}>
            <a href="/privacy-policy" className={styles.footerLink}>
              Privacy Policy
            </a>
            <span className={styles.footerCopy}>© Accountable 2025</span>
          </div>
          <div className={styles.socialIconsContainer}>
            <a
              href="https://www.linkedin.com/your-page"
              target="_blank"
              rel="noopener noreferrer"
            >
              <img
                src="/linkedin.svg"
                alt="LinkedIn"
                className={styles.socialIcon}
              />
            </a>
            <a
              href="https://www.youtube.com/your-channel"
              target="_blank"
              rel="noopener noreferrer"
            >
              <img src="/youtube.svg" alt="YouTube" className={styles.socialIcon} />
            </a>
          </div>
        </div>
      </footer>
    </div>
  );
}

// Export the wrapped component
export default ContactWithRecaptcha; 
