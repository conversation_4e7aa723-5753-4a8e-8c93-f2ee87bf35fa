"use client";

import { useState, useEffect, useRef } from "react";
import Image from "next/image";
import styles from "../app/page.module.css";

export default function HeroSection() {
  const [isVideoOpen, setIsVideoOpen] = useState(false);
  const [animationsLoaded, setAnimationsLoaded] = useState(false);
  const desktopCircleRef = useRef(null);
  const mobileCircleRef = useRef(null);

  useEffect(() => {
    if (isVideoOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "auto";
    }

    return () => {
      document.body.style.overflow = "auto";
    };
  }, [isVideoOpen]);

  // Defer animation initialization until after page load
  useEffect(() => {
    // Use requestIdleCallback if available, otherwise use setTimeout
    const deferredInit = window.requestIdleCallback || ((cb) => setTimeout(cb, 1000));

    // Wait for the page to be fully loaded
    if (document.readyState === 'complete') {
      deferredInit(() => setAnimationsLoaded(true));
    } else {
      window.addEventListener('load', () => {
        deferredInit(() => setAnimationsLoaded(true));
      });
    }

    return () => {
      window.removeEventListener('load', () => {
        deferredInit(() => setAnimationsLoaded(true));
      });
    };
  }, []);

  const openVideo = () => {
    setIsVideoOpen(true);
  };

  const closeVideo = () => {
    setIsVideoOpen(false);
  };

  return (
    <section className={styles.heroSection}>
        <div className={styles.heroContent}>
          <div className={styles.heroTextContent}>
            <h1 className={styles.heroTitle}>
              Where Meetings Lead to Milestones
            </h1>
            <p className={styles.heroDescription}>
              Introducing account-able, a centralized hub that turns recurring meetings into structured, results-driven sessions where decisions are made, tasks are assigned, and progress is tracked—all in one place.
            </p>
            <div className={styles.heroButtons}>
              <a href="/contact" className={styles.primaryButton}>Get a Demo</a>
              <button className={styles.secondaryButton} onClick={openVideo}>
                Watch Video
                <img src="/hero-play.svg" className={styles.playIcon} alt="Play" />
                <img src="/hero-play-hover.svg" className={styles.playIconHover} alt="Play" />
              </button>
            </div>
          </div>
        </div>
        
        {/* Mobile dashboard view */}
        <div className={styles.mobileDashboardContainer} onClick={openVideo}>
          <Image
            src="/Dashboard2.2.webp"
            alt="Dashboard Screenshot"
            className={styles.mobileDashboardImage}
            width={926}
            height={538}
            priority={true}
            quality={85}
          />
          <div 
            className={`${styles.videoTextCircle} ${animationsLoaded ? styles.animationLoaded : ''}`}
            ref={mobileCircleRef}
          >
            <svg viewBox="0 0 100 100" width="100" height="100">
              <defs>
                <path id="circle-path" d="M 50,50 m -30,0 a 30,30 0 1,1 60,0 a 30,30 0 1,1 -60,0" />
              </defs>
              <text>
                <textPath href="#circle-path" startOffset="0%" className={styles.rotatingText}>
                  WATCH THE VIDEO
                </textPath>
                <textPath href="#circle-path" startOffset="50%" className={styles.rotatingText}>
                  WATCH THE VIDEO
                </textPath>
              </text>
            </svg>
            <div className={styles.playButton}></div>
          </div>
        </div>
        
        {/* Desktop grid-based dashboard section */}
        <div className={styles.dashboardGrid}>
          {/* Left column - Video popup */}
          <div className={styles.dashboardLeftCol}>
            <figure className={styles.videoFigure} onClick={openVideo}>
              <img 
                src="/images/popup.webp" 
                alt="Watch video" 
                className={styles.videoPopupImage} 
              />
              <div 
                className={`${styles.videoTextCircle} ${animationsLoaded ? styles.animationLoaded : ''}`}
                ref={desktopCircleRef}
              >
                <svg viewBox="0 0 100 100" width="100" height="100">
                  <defs>
                    <path id="circle-path" d="M 50,50 m -30,0 a 30,30 0 1,1 60,0 a 30,30 0 1,1 -60,0" />
                  </defs>
                  <text>
                    <textPath href="#circle-path" startOffset="0%" className={styles.rotatingText}>
                      WATCH THE VIDEO
                    </textPath>
                    <textPath href="#circle-path" startOffset="50%" className={styles.rotatingText}>
                      WATCH THE VIDEO
                    </textPath>
                  </text>
                </svg>
                <div className={styles.playButton}></div>
              </div>
            </figure>
          </div>
          
          {/* Middle column - Dashboard image */}
          <div className={styles.dashboardMiddleCol}>
            <Image
              src="/Dashboard2.2.webp"
              alt="Dashboard Screenshot"
              className={styles.dashboardImage}
              width={926}
              height={538}
              priority={true}
              quality={100}
            />
          </div>
          
          {/* Right column - Hero images */}
          <div className={styles.dashboardRightCol}>
            <figure className={styles.rightHeroFigureTop}>
              <img 
                src="/images/righthero1.png" 
                alt="Feature highlight" 
                className={styles.rightHeroImage} 
              />
            </figure>
            <figure className={styles.rightHeroFigureBottom}>
              <img 
                src="/images/righthero2.0.png" 
                alt="Feature highlight" 
                className={styles.rightHeroImage} 
              />
            </figure>
          </div>
        </div>

        {/* Video popup overlay */}
        <div className={`${styles.videoOverlay} ${isVideoOpen ? styles.visible : ''}`}>
          <div className={styles.videoContainer}>
            <button className={styles.videoCloseButton} onClick={closeVideo}>×</button>
            {isVideoOpen && (
              <iframe
                className={styles.videoIframe}
                src="https://www.youtube.com/embed/AD8QvTbDoug?autoplay=1"
                title="account-able Video"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowFullScreen
              />
            )}
          </div>
        </div>
      </section>
  );
}
