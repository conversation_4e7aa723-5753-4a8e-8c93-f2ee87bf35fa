/* Privacy Policy page styles */
.privacyPage {
  width: 100%;
  background: linear-gradient(#04ab9e 0%, #03c1a6 100%);
  border-radius: 0 0 75px 75px;
  position: relative;
  z-index: 1;
  margin-bottom: -75px;
}

.heroSection {
  padding: 40px 20px 108px;
  margin: 0 auto;
  text-align: center;
  padding-top: 138px;
  background: url(/hero2560.webp) top / cover no-repeat, linear-gradient(#04ab9e 0%, #03c1a6 100%);
  position: relative;
}

.heroContainer {
  color: white;
}

.lastUpdated {
  background-color: rgba(0, 0, 0, 0.2);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  display: inline-block;
  margin-bottom: 12px;
  margin-top: 24px;
}

.heroTitle {
  font-size: 64px;
  font-weight: 600;
  color: white;
  margin: 0;
  line-height: 1.2;
}

.contentSection {
  background-color: #fff;
  padding: 100px 20px 80px;
  position: relative;
  z-index: 0;
}

.contentContainer {
  max-width: 800px;
  margin: 0 auto;
  color: #44505C;
  line-height: 1.6;
}

.sectionTitle {
  color: #44505C;
  font-size: 42px;
  font-weight: 600;
  line-height: 48px;
  margin-top: 48px;
  margin-bottom: 24px;
}

.contentContainer p {
  color: #44505C;
  font-size: 16px;
  line-height: 25px;
  font-weight: 300;
  margin-bottom: 16px;
}

.contentContainer .boldText {
  font-weight: 600;
}

.contentContainer h3 {
  color: #44505C;
  font-size: 20px;
  font-weight: 600;
  margin-top: 32px;
  margin-bottom: 16px;
}

.contentContainer ul {
  margin: 16px 0;
  padding-left: 24px;
}

.contentContainer li {
  color: #44505C;
  font-size: 16px;
  line-height: 25px;
  font-weight: 300;
  margin-bottom: 8px;
}

.contentContainer strong {
  font-weight: 600;
}

.contentContainer b {
  font-weight: 600;
}

.emailLink {
  color: #30C8AF;
  text-decoration: none;
}

.emailLink:hover {
  text-decoration: underline;
}

/* Footer Bar */
.footerBar {
  background-color: #000;
  padding: 16px 100px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
}

.footerLinks {
  display: flex;
  justify-content: flex-start;
  gap: 32px;
  align-self: center;
}

.footerLink,
.footerCopy {
  color: rgb(255, 255, 255);
  font-size: 14px;
  line-height: 1.4;
  font-weight: 300;
}

.socialIconsContainer {
  display: flex;
  gap: 16px;
  display: none;
}

.socialIcon {
  width: 42px;
  height: 42px;
}

/* Responsive styles */
@media (max-width: 991px) {
  .heroSection {
  padding-bottom: 58px;
}
  .heroTitle {
    font-size: 48px;
  }
  
  .footerBar {
    padding: 16px 20px;
    display: flex;
    flex-direction: row;
  }
  
  .footerLinks {
    justify-content: flex-start;
    align-self: center;
    gap: 16px;
    display: flex;
    flex-direction: row;
  }
  
  .socialIconsContainer {
    gap: 8px;
  }
}

@media (max-width: 768px) {
  .heroTitle {
    font-size: 36px;
  }
  
  .contentContainer h2 {
    font-size: 24px;
  }
  
  .contentContainer h3 {
    font-size: 18px;
  }
  
  .contactLogo {
    max-width: 300px;
  }
}




