.heroSection {
  border-radius: 0px 0px 75px 75px;
  padding: 138px 132px 94px;
  background:
    url("/hero2560.webp") center center / cover no-repeat,
    linear-gradient(180deg, #04AB9E 0%, #03C1A6 100%);
  position: relative;
  z-index: 1;
}

.heroContent {
  display: flex;
  gap: 48px;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  text-align: center;
  padding-bottom: 50px;
  padding-top: 20px;
}

.heroTextContent {
  flex: 1;
}

.heroTitle {
  color: #fff;
  font-size: 64px;
  font-weight: 600;
  line-height: 73px;
  margin-bottom: 24px;
}

.highlightedText {
  color: #03c0a6;
}

.heroDescription {
  color: #fff;
  font-size: 18px;
  line-height: 1.6;
  margin-bottom: 32px;
  opacity: 0.8;
  max-width: 900px;
  margin: 0 auto;
  padding-bottom: 30px;
  font-weight: 400;
}

.heroButtons {
  display: flex;
  gap: 0;
  justify-content: center;
}

.primaryButton {
  padding: 16px 32px;
  background-color: transparent;
  color: #fff;
  border-radius: 8px;
  font-weight: 500;
  font-family: 'sfCompact';
  font-size: 16px;
  border: 2px solid #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-block;
  text-decoration: none;
}

.primaryButton:hover {
  background-color: #ffffff;
  color: #03c0a7;
  border: none;
  border: 2px solid #ffffff;
}

.secondaryButton {
  padding: 16px 32px;
  background-color: transparent;
  color: #fff;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  font-family: 'sfCompact';
  font-size: 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
}

.playIcon {
  width: 18px;
  height: 18px;
  margin-top: 1px;
  transition: all 0.3s ease;
  display: block;
}

/* Hide the hover version by default */
.playIconHover {
  display: none;
  width: 18px;
  height: 18px;
  margin-top: 1px;
}

/* On hover, hide the normal icon and show the hover icon */
.secondaryButton:hover .playIcon {
  display: none;
}

.secondaryButton:hover .playIconHover {
  display: block;
}

/* Mobile dashboard container */
.mobileDashboardContainer {
  display: none;
  position: relative;
  width: 100%;
  margin-top: 30px;
  overflow: hidden;
  border-radius: 12px;
}

.mobileDashboardImage {
  width: 100%;
  height: auto;
  border-radius: 8px;
}

.mobileDashboardContainer .videoTextCircle {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.mobileDashboardContainer .videoTextCircle svg {
  position: absolute;
  width: 100px;
  height: 100px;
  animation: rotateText 10s linear infinite;
  fill: #fff;
}

.mobileDashboardContainer .playButton {
  width: 0;
  height: 0;
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
  border-left: 10px solid white;
  position: absolute;
  z-index: 2;
}

.mobileDashboardContainer .videoTextCircle text {
  font-size: 8px;
  font-weight: 600;
  fill: #fff;
  letter-spacing: 1px;
}

@media (max-width: 991px) {
  .heroSection {
    padding: 100px 24px 50px;
    min-height: auto;
  }

  .heroContent {
    flex-direction: column;
    text-align: center;
    padding-bottom: 30px;
  }

  .heroTitle {
    font-size: 48px;
    line-height: 56px;
  }
  
  .heroDescription {
    font-size: 18px;
  }

  .dashboardGrid {
    display: none !important;
  }
  
  .mobileDashboardContainer {
    display: block;
  }
  
  /* Hide feature card overlays on mobile/tablet */
  .featureImageOverlay {
    display: none;
  }
  
  /* Benefits section adjustments for mobile/tablet */
  .benefitsBox {
    padding: 45px 38px !important;
  }
  
  .benefitsGrid {
    margin-bottom: 24px !important;
  }
  
  /* Integrations grid for mobile/tablet */
  .integrationsGrid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 32px;
  }
}

@media (max-width: 640px) {
  .heroSection {
    padding: 160px 20px 60px;
  }
  
  .heroTitle {
    font-size: 40px;
    line-height: 48px;
    font-weight: 600;
  }
  
  .heroDescription {
    font-size: 20px;
    font-weight: 400;
    line-height: 1.5;
  }

  .heroButtons {
    flex-direction: row;
    align-items: center;
    gap: 0;
  }
}

/* Features Section */
.featuresSection {
  background-color: #ECF3F7;
  padding: 80px 20px 140px;
  overflow: visible;
}

.featuresContainer {
  max-width: 1200px;
  margin: 0 auto;
  overflow: visible;
}

.featuresIntro {
  margin-bottom: 60px;
}

.featuresLabel {
      color: #66717C;
    background-color: #E2EEF5;
    border-radius: 6px;
    margin-bottom: 16px;
    padding: 8px 12px;
    font-size: 16px;
    font-weight: 700;
    display: inline-flex; 
    line-height: 150%;
}

.featuresTitle {
    color: #44505C;
    font-size: 42px;
    font-weight: 600;
    line-height: 48px;
    max-width: 830px;
    margin-top: 23px;
}

.featuresSubtitle {
    color: #44505C;
    max-width: 800px;
    margin: 38px 0;
    font-size: 16px;
    line-height: 25px;
    font-weight: 300;
}

.featuresGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 35px;
  overflow: visible;
}

.featureCard {
    background-color: #fff;
    flex-direction: column;
    display: flex;
    overflow: visible;
    border-radius: 8.621px 8.621px 50px 8.621px;
}

.featureImageContainer {
    position: relative;
    width: 100%;
    overflow: visible;
}

.featureImage {
  width: 100%;
  height: 208px;
  display: block;
  object-fit: cover;
}

/* Base overlay style */
.featureImageOverlay {
  position: absolute;
  z-index: 2;
}

/* Specific overlay styles for each card */
.featureCard:nth-child(1) .featureImageOverlay {
  top: 0;
  left: 0;
  width: 146px;
  height: auto;
  transform: translateY(30px);
  margin-left: -35px;
}

.featureCard:nth-child(2) .featureImageOverlay {
  top: 0;
  left: 0;
  width: 160px;
  height: auto;
  transform: translateY(25px);
  margin-left: -25px;
}

.featureCard:nth-child(3) .featureImageOverlay {
  bottom: 0;
  right: 0;
  width: 237.49px;
  height: auto;
  transform: translateY(-20px);
  margin-right: -91px;
}

.featureContent {
  padding: 0 32px 32px;
}

.featureCardTitle {
    color: #44505C;
    margin: 22px 0 16px;
    font-size: 24px;
    font-weight: 500;
    line-height: 32px;
}

.featureCardText {
  font-size: 16px;       
  color: #44505C;
  margin: 0;
  line-height: 25px;
}


/* ── How It Works Section Styles ───────────────────────────────────────── */

.howItWorksSection {
  background-color: #F6F9FB;
  padding: 80px 20px;
}

.howItWorksContainer {
  max-width: 1200px;
  margin: 0 auto;
}

/* Header */
.howItWorksHeader {
  text-align: center;
  margin-bottom: 60px;
}
.howItWorksLabel {
    display: inline-block;
    color: #66717c;
    background-color: #e2eef5;
    border-radius: 6px;
    margin-bottom: 32px;
    padding: 8px 12px;
    font-size: 16px;
    font-weight: 700;
    line-height: 150%;
}
.howItWorksTitle {
    color: #44505c;
    max-width: 640px;
    font-size: 42px;
    font-weight: 700;
    line-height: 48px;
    margin: 0 auto;
}

/* Steps nav */
.stepsNav {
  display: flex;
  justify-content: space-between;
  list-style: none;
  padding: 0;
  margin: 0 0 24px;
}

.stepItem {
  text-align: left;
  padding: 12px 16px;
  border-radius: 8px;
  cursor: pointer;
  background-color: transparent;
}

.stepItemActive {
  background-color: #61E0C8;
  color: #ffffff;
}

.stepNumber {
  display: block;
  font-size: 0.875rem;
  color: inherit;
  opacity: 0.75;
  margin-bottom: 4px;
}

.stepText {
  font-size: 1rem;
  font-weight: 600;
  color: inherit;
}

/* Divider */
.stepsDivider {
  border: none;
  height: 1px;
  background-color: #E2E8F0;
  margin-bottom: 40px;
}

/* Content area */
.stepsContent {
  display: flex;
  gap: 24px;
  align-items: flex-start;
}

/* Slider placeholder */
.sliderPlaceholder {
  flex: 1;
  background-color: #ffffff;
  border-radius: 8px;
  height: 300px; /* adjust to match your slider height */
}

/* Details panel */
.stepDetails {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.stepBadge {
  display: inline-block;
  font-size: 0.75rem;
  color: #475569;
  background-color: #F1F5F9;
  padding: 4px 8px;
  border-radius: 4px;
  margin-bottom: 16px;
}

.stepDetailsTitle {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1E293B;
  margin-bottom: 16px;
}

.stepDetailsText {
  font-size: 1rem;
  color: #475569;
  margin-bottom: 24px;
}

.stepList {
  list-style: none;
  padding: 0;
  margin: 0 0 32px;
}

.stepListItem {
  position: relative;
  padding-left: 20px;
  margin-bottom: 12px;
  font-size: 0.9375rem;
  color: #334155;
}

.stepListItem::before {
  content: "";
  position: absolute;
  left: 0;
  top: 6px;
  width: 8px;
  height: 8px;
  background-color: #1E293B;
  border-radius: 2px;
}

/* Nav buttons under details */
.stepNavButtons {
  display: flex;
  gap: 16px;
}

.navButton {
  background-color: #61E0C8;
  border: none;
  border-radius: 4px;
  padding: 12px;
  cursor: pointer;
  font-size: 1rem;
  color: #ffffff;
}

/* ── Benefits & Comparison Section ─────────────────────────────────────── */

.benefitsSection {
  background-color: #FFF;
  padding: 80px 20px;
}

.benefitsContainer {
  max-width: 1200px;
  margin: 0 auto;
}

/* Header */
.benefitsIntro {
  text-align: center;
  margin-bottom: 40px;
}

.benefitsLabel {
    display: inline-block;
    color: #66717c;
    background-color: #e2eef5;
    border-radius: 6px;
    margin-bottom: 32px;
    padding: 8px 12px;
    font-size: 16px;
    font-weight: 700;
    line-height: 150%;
}

.benefitsTitle {
    color: #44505c;
    max-width: 640px;
    font-size: 42px;
    font-weight: 600;
    line-height: 48px;
    margin: 0 auto;
}

/* Two-column grid */
.benefitsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

/* Shared box styles */
.benefitsBox {
  padding: 45px 55px;
  border-radius: 26px 26px 100px 26px;
}

/* Pink box */
.pinkBox {
  background: #FFDEE2;
}

/* Green box */
.greenBox {
  background: #D6F4EF;
}

/* Box titles */
.benefitsBoxTitle {
    color: #44505C;
    margin-bottom: 28px;
    margin-top: 0;
    font-size: 32px;
    font-weight: 600;
}

/* List */
.benefitsList {
  list-style: none;
  padding: 0;
  margin: 0;
}

.benefitsListItem {
color: #66717C;
    margin-bottom: 22px;
    font-size: 16px;
    line-height: 25px;
    font-weight: 400;
}
.benefitsListItem b {
    font-weight: 600;
}

/* Bottom call-to-action */
.ctaBox {
  background: #EAECFC;
  border-radius: 26px;
  text-align: center;
  padding: 60px 20px;
}

.ctaTitle {
    color: #44505c;
    max-width: 640px;
    font-size: 42px;
    font-weight: 600;
    line-height: 48px;
    margin: 0 auto;
    margin-bottom: 32px;
}

.ctaText {
    color: #44505C;
    max-width: 423px;
    margin: 0 auto 36px;
    font-size: 16px;
    font-weight: 300;
}

/* You can reuse your existing .primaryButton or define a new one: */
.ctaButton {
  background-color: #747FED;
  color: #ffffff;
  box-shadow: 0px 3px 15px 0px #747FED;
  border: none;
  border-radius: 6px;
  padding: 16px 32px;
  font-size: 16px;
  font-weight: 600;
  font-family: "sfCompact", sans-serif;
  cursor: pointer;
  display: inline-block;
  text-decoration: none;
  transition: all 0.3s ease;
  border: 1px solid #747FED;
}

.ctaButton:hover {
  background-color: transparent;
  color: #747FED;
  border: 1px solid #747FED;
  box-shadow: none;
}

/* Bullet-icons for pink vs. green lists */
.pinkBox .benefitsListItem,
.greenBox .benefitsListItem {
  position: relative;
  padding-left: 48px;
}

.pinkBox .benefitsListItem::before {
  content: "";
  position: absolute;
  left: 0;
  top: -2px;             
  width: 32px;
  height: 32px;
  background: url("/Ellipse 64.svg") no-repeat center center;
  background-size: contain;
}

.greenBox .benefitsListItem::before {
  content: "";
  position: absolute;
  left: 0;
  top: -2px;             
  width: 32px;
  height: 32px;
  background: url("/Ellipse 86.svg") no-repeat center center;
  background-size: contain;
}

/* ── Integrations Section ─────────────────────────────────────────────── */

.integrationsSection {
  background-color: #ECF3F7;
  padding: 80px 20px;
}

.integrationsContainer {
  max-width: 1200px;
  margin: 0 auto;
  text-align: center;
}

.integrationsTitle {
    color: #44505C;
    margin-bottom: 64px;
    font-size: 42px;
    font-weight: 600;
    line-height: 48px;
}

.integrationsGrid {
  display: grid;
  grid-template-columns: repeat(6, minmax(100px, 1fr));
  gap: 24px;
  align-items: center;
  justify-items: center;
  margin-bottom: 24px;
}

.integrationLogo {
  width: 100%;
  height: auto;
}

.integrationsFooter {
    color: #44505C;
    margin-top: 40px;
    font-size: 16px;
    line-height: 25px;
    font-weight: 300;
}

.dashboardGrid {
  display: grid;
  grid-template-columns: 20% 60% 20% !important;
  gap: 0 !important;
  margin-top: 90px;
  margin-bottom: 90px;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

.dashboardLeftCol, .dashboardMiddleCol, .dashboardRightCol {
  position: relative;
}

.dashboardMiddleCol {
  display: flex;
  justify-content: center;
  align-items: center;
  transform: scale(1.3);
}

.dashboardImage {
  max-width: 100%;
  height: auto;
}

/* Video figure styling */
.videoFigure {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  margin: 0;
  transform: translateY(50px);
  z-index: 10;
  width: 262px;
  transition: filter 0.3s ease;
}

.videoFigure:hover {
  filter: brightness(0.65);
}

.videoPopupImage {
  max-width: 100%;
  border-radius: 12px 12px 12px 50px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* Right hero figures */
.rightHeroFigureTop {
  margin-left: 45px;
    transform: translateY(8px);
}

.rightHeroFigureBottom {
  margin-left: -90px;
    transform: translateY(33px);
}

.rightHeroImage {
  max-width: 100%;
  border-radius: 12px;
}

.rightHeroFigureTop .rightHeroImage {
  max-width: 185px;
}

.rightHeroFigureBottom .rightHeroImage {
  max-width: 321px;
  border: 1px solid rgba(102, 113, 124, 0.30);
}

/* Video circle styling */
.videoTextCircle {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 87px;
  height: 87px;
  pointer-events: none;
  border-radius: 50%;
  background-color: #747FED;
  overflow: visible;
}

.videoTextCircle svg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  /* Disable animation by default */
  animation: none;
}

/* Only apply animation when explicitly loaded */
.videoTextCircle.animationLoaded svg {
  animation: rotateText 14s linear infinite;
}

.videoTextCircle text {
  fill: white;
  font-family: 'Arial', sans-serif;
  font-weight: normal;
  font-size: 7.5px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.playButton {
  position: absolute;
  top: 50%;
  left: 52%;
  transform: translate(-50%, -50%);
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 6px 0 6px 10px;
  border-color: transparent transparent transparent white;
  z-index: 2;
}

@keyframes rotateText {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.videoFigure:hover .videoTextCircle.animationLoaded svg {
  animation-duration: 7s;
  animation-play-state: running;
}

/* Mobile version */
.mobileDashboardContainer .videoTextCircle svg {
  animation: none;
}

.mobileDashboardContainer .videoTextCircle.animationLoaded svg {
  animation: rotateText 14s linear infinite;
}

/* Video overlay and container styles */
.videoOverlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.videoOverlay.visible {
  opacity: 1;
  visibility: visible;
}

.videoContainer {
  width: 80%;
  max-width: 900px;
  position: relative;
}

.videoCloseButton {
  position: absolute;
  top: -40px;
  right: 0;
  background: transparent;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
}

.videoIframe {
  width: 100%;
  aspect-ratio: 16/9;
  border: none;
}

/* Responsive adjustments */
@media (max-width: 991px) {
  .dashboardGrid {
    grid-template-columns: 1fr !important;
  }
  
  .dashboardLeftCol, .dashboardRightCol {
    display: none;
  }
}
