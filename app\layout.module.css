.header {
  max-width: 1440px;
  width: 100%;
  z-index: 100;
  background: transparent;
  margin: 0 auto;
  margin-bottom: -138px;
  position: relative;
}

.navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 132px;
}

.logo {
  width: 214px;
  height: 26px;
}

.navLinks {
  display: flex;
  gap: 64px;
  display: none;
}

.navItem {
  color: #fff;
  font-size: 16px;
  font-weight: 700;
}

.ctaContainer {
  display: flex;
  align-items: center;
  gap: 24px;
}

.loginButton {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #fff;
  transition: color 0.3s ease;
  cursor: pointer;
}

.lockIcon {
  width: 17px;
  height: 17px;
  transition: all 0.3s ease;
}

.loginButton:hover .lockIcon {
  content: url('/lock-icon-hover.svg');
}

.loginText {
  font-size: 16px;
  font-weight: 500;
}

.freeBetaButton {
  border: 1px solid #fff;
  border-radius: 6px;
  padding: 13px 0;
  width: 131px;
  color: #fff;
  font-size: 16px;
  font-weight: 700;
  text-align: center;
  background-color: #ff5c6c;
  display: inline-block;
  text-decoration: none;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.freeBetaButton:hover {
  background-color: #fff;
  color: #ff5c6c;
  border: 1px solid #ff5c6c;
}

.demoButton {
  border-radius: 6px;
  padding: 13px 0;
  width: 131px;
  color: #03c0a6;
  font-size: 16px;
  font-weight: 700;
  text-align: center;
  box-shadow: 0px 3px 15px rgba(27, 195, 167, 0.3);
  background-color: #fff;
  display: inline-block;
  text-decoration: none;
  transition: background-color 0.3s ease, color 0.3s ease;
  border: 1px solid #03c0a6;
}

.demoButton:hover {
  background-color: #03c0a6;
  color: #fff;
  border: 1px solid #fff;
}

.hamburgerMenu {
  display: none;
  flex-direction: column;
  gap: 6px;
  cursor: pointer;
  z-index: 200;
}

.hamburgerBar {
  width: 24px;
  height: 2px;
  background-color: #fff;
  transition: all 0.3s ease;
}

.mobileMenu {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(180deg, #04AB9E 0%, #03C1A6 100%);
  z-index: 100;
  padding: 80px 24px 24px;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.mobileMenu.open {
  display: flex;
}

.mobileMenu .navLinks {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  margin-bottom: 24px;
}

.mobileMenu .navItem {
  color: #fff;
  font-size: 20px;
  font-weight: 600;
}

.mobileMenu .loginButton,
.mobileMenu .freeBetaButton,
.mobileMenu .demoButton {
  margin-top: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
}

@media (max-width: 991px) {
  .navigation {
    padding: 24px;
  }
  
  .logo {
    width: 170px;
    height: auto;
  }
  
  .navLinks, .ctaContainer {
    display: none;
  }
  
  .hamburgerMenu {
    display: flex;
  }
  
  /* Footer responsive styles */
  .footerWrapper {
    padding-right: 20px;
    padding-left: 20px;
  }
  
  .footerBar {
    padding: 16px 20px;
    display: flex;
    flex-direction: row;
  }
  
  .footerLinks {
    justify-content: flex-start;
    align-self: center;
    gap: 16px;
    display: flex;
    flex-direction: row;
  }
  
  .socialIconsContainer {
    gap: 8px !important;
  }
}

@media (max-width: 640px) {
  .navigation {
    padding: 16px 24px;
  }
  
  .logo {
    width: 121px;
    height: auto;
  }
  
  /* More footer responsive styles */
  .footerHeading {
    font-size: 32px;
    line-height: 38px;
  }
}

/* ── Footer / CTA Section ── */

.footerWrapper {
  background-color: #FFFFFF;
  position: relative;
  margin-top: -75px; /* Pull up to create overlap with previous section */
  padding-top: 75px; /* Add padding equal to border-radius to maintain spacing */
}

.footerSection {
  background: linear-gradient(180deg, #04AB9E 0%, #03C1A6 100%);
  border-radius: 75px 75px 0 0;
  padding: 80px 0 0;
  color: #fff;
  text-align: center;
  overflow: hidden;
}

.footerInner {
  max-width: 1440px;
  margin: 0 auto;
  padding: 0 20px;
}

.footerHeading {
    margin-bottom: 28px;
    font-weight: 600;
    color: #FFF;
    text-align: center;
    font-size: 42px;
    font-style: normal;
    line-height: 48px;
}

.footerText {
    margin-bottom: 20px;
    font-size: 16px;
    font-style: normal;
    font-weight: 300;
    line-height: 25px;
}

.footerButton {
    color: #fff;
    cursor: pointer;
    margin-bottom: 30px;
    padding: 16px 32px;
    font-size: 16px;
    font-family: "sfCompact", sans-serif;
    font-weight: 600;
    border-radius: 6px;
    border: 1px solid #FFF;
    background: #FF5C6C;
    box-shadow: 0px 3px 15px 0px rgba(27, 195, 167, 0.30);
    display: inline-block;
    text-decoration: none;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.footerButton:hover {
  background-color: #fff;
  color: #ff5c6c;
  border: 1px solid #ff5c6c;
}

.footerImageWrapper {
  margin-bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: visible;
}

.footerImage {
    max-width: 100%;
    height: auto;
    display: block;
}

/* Black social-icon bar */
.footerBar {
  background-color: #000;
  padding: 16px 100px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
}

@media (max-width: 991px) {
  .footerBar {
    padding: 16px 20px;
    display: flex;
    flex-direction: row;
  }
  .footerSection {
    padding: 50px 0 0;
  }
}

.socialIconsContainer {
  display: flex;
  gap: 16px;
  display: none;
}

.socialIcon {
  width: 42px;
  height: 42px;
}

/* Footer links row */
.footerLinks {
  display: flex;
  justify-content: flex-start; 
  gap: 32px;
  align-self: center;
}

.footerLink,
.footerCopy {
  color: rgb(255, 255, 255);
  font-size: 14px;     
  line-height: 1.4;
  font-weight: 300;
}

.footerLink {
  text-decoration: none;
  transition: text-decoration 0.3s ease;
}

.footerLink:hover {
  text-decoration: underline;
}

