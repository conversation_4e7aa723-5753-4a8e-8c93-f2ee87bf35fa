/* HowItWorksSection.module.css */

.howItWorksSection {
  background-color: #F6F9FB;
  padding: 80px 20px;
}

.howItWorksContainer {
  max-width: 1200px;
  margin: 0 auto;
}

/* Header */
.howItWorksHeader {
  text-align: center;
  margin-bottom: 60px;
}

.howItWorksLabel {
  display: inline-block;
  color: #66717C;
  background-color: #E2EEF5;
  border-radius: 6px;
  margin-bottom: 32px;
  padding: 8px 12px;
  font-size: 16px;
  font-weight: 700;
  line-height: 150%;
}

.howItWorksTitle {
  color: #44505C;
  max-width: 640px;
  font-size: 42px;
  font-weight: 600;
  line-height: 48px;
  margin: 0 auto;
}

/* Main container with fixed size */
.mainContainer {
  background-color: #ECF3F7;
  border-radius: 25px;
  overflow: hidden;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  padding: 30px 40px 45px;
  margin-bottom: 10px;
}

/* Steps Navigation */
.stepsNav {
  display: flex;
  justify-content: space-between;
  padding: 0;
  margin: 0;
  gap: 30px;
  border-bottom: 1px solid #ffffff;
  background-color: #ECF3F7;
  padding-bottom: 27px;
}

.stepItem {
  flex: 1;
  text-align: left;
  padding: 24px 20px;
  height: 114px;
  box-sizing: border-box;
  cursor: pointer;
  background-color: transparent;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: center;
  transition: background-color 0.3s ease;
  border-radius: 8px;
}

.stepItemActive {
  background-color: rgba(27, 195, 167, 0.60);
}

.stepItemActive .stepNumber,
.stepItemActive .stepText {
  color: #FFFFFF;
  opacity: 1;
}

.stepNumber {
  display: block;
  font-size: 15px;
  color: #31C8AF;
  opacity: 0.75;
  margin-bottom: 16px;
  font-weight: 600;
}

.stepText {
  font-size: 15px;
  font-weight: 600;
  color: #66717C;
  display: block;
  line-height: 1.4;
  padding-right: 30px;
}

/* Progress bar for active step - covers the entire box */
.progressBarContainer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
  z-index: 0;
  overflow: hidden;
}

.progressBar {
  height: 100%;
  width: 0%;
  background-color: #30C8AF;
  transition: width 10s linear;
  opacity: 0.6;
}

/* Make text appear above progress bar */
.stepNumber, .stepText {
  position: relative;
  z-index: 1;
}

/* Content area */
.stepsContent {
  display: flex;
  align-items: stretch;
  background-color: #ECF3F7;
  padding-top: 44px;
}

/* Image container - left side */
.imageContainer {
  width: 549px;
  position: relative;
}

.imageWrapper {
  width: 549px;
  height: 440px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stepImage {
  width: 549px;
  height: 440px;
  object-fit: cover;
  border-radius: 8px;
}

/* Details panel - right side */
.stepDetails {
  width: calc(100% - 549px);
  display: flex;
  flex-direction: column;
  padding: 0 0 0 60px;
  background-color: #ECF3F7;
  position: relative;
  min-height: 440px;
  justify-content: flex-start;
}

.stepBadge {
  display: inline-block;
  font-size: 16px;
  font-weight: 700;
  color: #66717C;
  background-color: #E2EEF5;
  padding: 10px 14px;
  border-radius: 6px;
  width: fit-content;
}

.stepDetailsTitle {
  font-size: 32px;
  font-weight: 600;
  color: #44505C;
  margin-bottom: 16px;
  margin-top: 16px;
  line-height: 1.2;
}

.stepDetailsText {
  font-size: 16px;
  color: #66717C;
  margin-bottom: 32px;
  line-height: 1.6;
  max-width: 90%;
  font-weight: 300;
}

/* Checkbox list */
.checkboxList {
  margin-bottom: 32px;
  display: flex;
  flex-wrap: wrap;
  gap: 12px 30px;
  max-width: 90%;
}

.checkboxItem {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
  width: calc(50% - 15px);
  min-width: 150px;
}

.checkboxItem input[type="checkbox"] {
  display: none;
}

.checkboxItem label {
  font-size: 16px;
  color: #44505C;
  line-height: 22px;
  font-weight: 300;
  position: relative;
  padding-left: 24px;
}

.checkboxItem label:before {
  content: "";
  position: absolute;
  left: 0;
  top: 5px;
  width: 12px;
  height: 12px;
  background-color: #44505C;
}

/* Navigation buttons */
.navigationButtons {
  display: flex;
  gap: 16px;
  margin-top: auto;
  position: absolute;
  bottom: 20px;
  right: 20px;
}

.navButton {
  width: 40px;
  height: 40px;
  background-color: #30C8AF;
  border: none;
  border-radius: 4px;
  color: #ffffff;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
  padding: 0;
}

.navButton:hover {
  background-color: #28B09A;
}

/* Mobile Accordion Styles */
.mobileAccordion {
  display: none;
  flex-direction: column;
  gap: 16px;
  margin: 0 auto;
  max-width: 600px;
}

.mobileAccordionItem {
  background-color: #F6F9FB;
  border-radius: 12px;
  overflow: hidden;
}

.mobileAccordionItemActive {
  background-color: #03C1A6;
}

.mobileAccordionHeader {
  display: flex;
  flex-direction: column;
  padding: 20px;
  cursor: pointer;
  position: relative;
  background-color: #ECF3F7;
}

.mobileAccordionItemActive .mobileAccordionHeader {
  background-color: #03C1A6;
}

.mobileStepNumber {
  font-size: 15px;
  font-weight: 600;
  color: #31C8AF;
  margin-bottom: 8px;
}

.mobileStepTitle {
  font-size: 15px;
  font-weight: 600;
  color: #66717C;
  padding-right: 24px;
}

.mobileAccordionIcon {
  position: absolute;
  top: 50%;
  right: 20px;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Active state styles */
.mobileAccordionItemActive .mobileStepNumber {
  color: #FFFFFF;
}

.mobileAccordionItemActive .mobileStepTitle,
.mobileAccordionItemActive .mobileAccordionIcon {
  color: #FFFFFF;
}

.mobileAccordionContent {
  padding: 0 20px 20px;
}

.mobileImageContainer {
  background-color: #FFFFFF;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 16px;
}

.mobileStepImage {
  width: 100%;
  height: auto;
  display: block;
}

.mobileStepDetailsTitle {
  font-size: 32px;
  font-weight: 600;
  color: #FFFFFF;
  margin-bottom: 12px;
  line-height: 1.3;
}

.mobileStepDescription {
  font-size: 16px;
  color: #FFFFFF;
  margin-bottom: 16px;
  line-height: 1.5;
  font-weight: 300;
}

.mobileItemsList {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.mobileItem {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  color: #FFFFFF;
  font-size: 16px;
  line-height: 1.4;
  font-weight: 300;
}

.mobileBulletPoint {
  display: block;
  width: 16px;
  height: 16px;
  background-color: #FFFFFF;
  margin-top: 5px;
  flex-shrink: 0;
  line-height: 25px;
}

/* Responsive styles */
@media (max-width: 1200px) {
  .imageContainer, .imageWrapper, .stepImage {
    width: 450px;
  }
}

@media (max-width: 991px) {
  .stepsContent {
    flex-direction: column;
  }
  
  .imageContainer, .imageWrapper, .stepImage {
    width: 100%;
    height: auto;
  }
  
  .stepDetails {
    width: 100%;
    padding: 30px 0 0;
  }
  
  .checkboxList {
    max-width: 100%;
  }
}

@media (max-width: 768px) {
  .stepsNav {
    flex-wrap: wrap;
  }
  
  .stepItem {
    flex: 0 0 100%;
  }
  
  .checkboxItem {
    width: 100%;
  }
  
  .mobileAccordion {
    display: flex;
  }
  
  .mainContainer {
    display: none;
  }
  
} 