/* components/FaqSection.module.css */

.faqSection {
  background-color: #ffffff;
  padding: 80px 20px;
}

.faqContainer {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 40px;
}

.faqIntro {
  display: flex;
  align-items: flex-start;
}

.faqTitle {
    color: #44505C;
    margin: 0;
    font-size: 32px;
    font-weight: 600;
    line-height: 36px;
    text-align: center;
}

.faqList {
  display: flex;
  flex-direction: column;
}

.faqItem {
  background-color: #ecf3f7;
  border-radius: 8px;
  margin-bottom: 16px;
  overflow: hidden;
}

.faqQuestion {
  width: 100%;
  background: transparent;
  border: none;
  padding: 25px;
  font-size: 18px;
  font-weight: 600;
  color: #44505C;
  text-align: left;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  font-family: inherit;
}

.faqIcon {
  font-size: 1.25rem;
  line-height: 1;
}

.faqAnswer {
  padding: 0 25px;
  font-size: 16px;
  color: #44505C;
  transition: all 0.6s ease;
  overflow: hidden;
}

.faqAnswer p {
  margin-top: 0;
  margin-bottom: 48px;
}

.faqAnswer a {
  text-decoration: underline;
}

.faqAnswer ul {
  margin: 0 0 48px 0;
  list-style-type: none;
  padding: 0;
}

.faqAnswer ul li {
  position: relative;
  padding-left: 42px;
  margin-bottom: 16px;
  color: #66717C;
  font-size: 16px;
  line-height: 22px;
}

.faqAnswer ul li::before {
  content: "";
  position: absolute;
  left: 0;
  top: -2px;             
  width: 25px;
  height: 25px;
  background: url("/checkmark.svg") no-repeat center center;
  background-size: contain;
}

.highlight {
  color: #04AD9F;
  font-weight: 600;
}

/* Responsive */
@media (max-width: 768px) {
  .faqContainer {
    grid-template-columns: 1fr;
  }
  
  .faqIntro {
    justify-content: center;
    margin-bottom: 30px;
  }
}
