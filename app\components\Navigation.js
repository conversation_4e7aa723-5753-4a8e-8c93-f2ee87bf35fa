"use client";

import { useState } from "react";
import { usePathname } from "next/navigation";
import styles from "../layout.module.css";
import Link from "next/link";

export default function Navigation() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const pathname = usePathname();
  
  // Check if current path is the contact page
  const isContactPage = pathname === "/contact";

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  if (isContactPage) {
    return null; // Don't render navigation on contact page
  }

  return (
    <header className={styles.header}>
      <nav className={styles.navigation}>
        <Link href="/" className={styles.logoLink} aria-label="Go to homepage">
          <img
            src="/logo.svg"
            alt="account-able logo"
            className={styles.logo}
          />
        </Link>

        <div className={styles.navLinks}>
          <div className={styles.navItem}>About Us</div>
          <div className={styles.navItem}>Features</div>
          <div className={styles.navItem}>Solutions</div>
        </div>

        <div className={styles.ctaContainer}>
          <div className={styles.loginButton}>
            <img src="/lock-icon.svg" alt="Lock" className={styles.lockIcon} />
            <div className={styles.loginText}>Login</div>
          </div>

          <a href="/contact" className={styles.freeBetaButton}>Free Beta</a>

          <a href="/contact" className={styles.demoButton}>Get a Demo</a>
        </div>

        <div className={styles.hamburgerMenu} onClick={toggleMobileMenu}>
          <div className={styles.hamburgerBar}></div>
          <div className={styles.hamburgerBar}></div>
        </div>
      </nav>

      {/* Mobile Menu */}
      <div className={`${styles.mobileMenu} ${mobileMenuOpen ? styles.open : ''}`}>
        <div className={styles.navLinks}>
          <div className={styles.navItem}>About Us</div>
          <div className={styles.navItem}>Features</div>
          <div className={styles.navItem}>Solutions</div>
        </div>
        <div className={styles.loginButton} style={{ marginTop: '24px' }}>
          <img src="/lock-icon.svg" alt="Lock" className={styles.lockIcon} />
          <div className={styles.loginText}>Login</div>
        </div>
        <a href="/contact" className={styles.freeBetaButton} style={{ marginTop: '24px' }}>Free Beta</a>
        <a href="/contact" className={styles.demoButton} style={{ marginTop: '16px' }}>Get a Demo</a>
      </div>
    </header>
  );
}

