import { sfCompact } from '../lib/fonts'
import "./globals.css";
import Navigation from './components/Navigation';
import Footer from './components/Footer';

export const metadata = {
  title: {
    default: 'account-able | Where Meetings Lead to Milestones',
    template: '%s | account-able'
  },
  description: 'Transform your meetings with account-able - a centralized hub that turns recurring meetings into structured, results-driven sessions where decisions are made, tasks are assigned, and progress is tracked.',
  keywords: [
    'meeting management',
    'team collaboration',
    'productivity',
    'meeting hub',
    'task tracking',
    'decision making',
    'team meetings',
    'project management'
  ],
  authors: [{ name: 'account-able' }],
  creator: 'account-able',
  publisher: 'account-able',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('http://account-able.io'), 
  alternates: {
    canonical: '/',
  },
  openGraph: {
    title: 'account-able | Where Meetings Lead to Milestones',
    description: 'Transform your meetings with account-able - a centralized hub that turns recurring meetings into structured, results-driven sessions.',
    url: 'http://account-able.io', 
    siteName: 'account-able',
    images: [
      {
        url: '/og-image.jpg', // You'll need to add this image
        width: 1200,
        height: 630,
        alt: 'account-able - Meeting Management Platform',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'account-able | Where Meetings Lead to Milestones',
    description: 'Transform your meetings with account-able - a centralized hub for structured, results-driven sessions.',
    images: ['/og-image.jpg'], // Same image as OpenGraph
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    // Add your verification codes here when you have them
    // google: 'your-google-verification-code',
    // yandex: 'your-yandex-verification-code',
    // yahoo: 'your-yahoo-verification-code',
  },
};

export default function RootLayout({ children }) {
  return (
    <html lang="en" className={sfCompact.className}>
      <head>
        {/* Critical font preloading for LCP - hero section only */}
        <link
          rel="preload"
          href="/fonts/SF-Compact-Text-Semibold.woff2"
          as="font"
          type="font/woff2"
          crossOrigin="anonymous"
        />

        {/* Prefetch other fonts for later use */}
        <link
          rel="prefetch"
          href="/fonts/SF-Compact-Text-Regular.woff2"
          as="font"
          type="font/woff2"
          crossOrigin="anonymous"
        />
        <link
          rel="prefetch"
          href="/fonts/SF-Compact-Text-Bold.woff2"
          as="font"
          type="font/woff2"
          crossOrigin="anonymous"
        />

        {/* Favicon and app icons */}
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />

        {/* Theme color for mobile browsers */}
        <meta name="theme-color" content="#03c0a7" />
        <meta name="msapplication-TileColor" content="#03c0a7" />

        {/* Viewport and mobile optimization */}
        <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover" />

        {/* Performance hints - only for external domains */}
        <link rel="dns-prefetch" href="//www.youtube.com" />

        {/* Security headers */}
        <meta httpEquiv="X-Content-Type-Options" content="nosniff" />
        <meta httpEquiv="Referrer-Policy" content="strict-origin-when-cross-origin" />

        {/* Critical CSS for font loading optimization */}
        <style dangerouslySetInnerHTML={{
          __html: `
            /* Immediate font fallback for hero section */
            .hero-title, .heroTitle {
              font-family: var(--font-sf-compact), system-ui, -apple-system, BlinkMacSystemFont, sans-serif;
              font-display: swap;
            }
            /* Prevent layout shift during font loading */
            * {
              font-display: swap;
            }
          `
        }} />
      </head>
      <body>
        <Navigation />

        {/* ── page content ── */}
        {children}

        <Footer />
      </body>
    </html>
  );
}
