import localFont from 'next/font/local'

export const sfCompact = localFont({
  src: [
    {
      path: '../fonts/SF-Compact-Text-Light.woff2',
      weight: '300',
      style: 'normal'
    },
    {
      path: '../fonts/SF-Compact-Text-Regular.woff2',
      weight: '400',
      style: 'normal'
    },
    {
      path: '../fonts/SF-Compact-Text-Medium.woff2',
      weight: '500',
      style: 'normal'
    },
    {
      path: '../fonts/SF-Compact-Text-Semibold.woff2',
      weight: '600',
      style: 'normal'
    },
    {
      path: '../fonts/SF-Compact-Text-Bold.woff2',
      weight: '700',
      style: 'normal'
    },
  ],
  variable: '--font-sf-compact',
  display: 'swap',
})