:root {
  --background: #ffffff;
  --foreground: #171717;
  --font-sf-compact: 'sfCompact', system-ui, -apple-system, sans-serif;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #ECF3F7;
    --foreground: #ededed;
  }
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  margin: 0;
  padding: 0;
  width: 100%;
  min-height: 100vh;
  font-family: var(--font-sf-compact), system-ui, -apple-system, BlinkMacSystemFont, sans-serif;
}

body {
  color: var(--foreground);
  background: var(--background);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  color: inherit;
  text-decoration: none;
}

@media (prefers-color-scheme: dark) {
  html {
    color-scheme: dark;
  }
}

/* app/globals.css */

.container {
  max-width: 960px;
  margin: 0 auto;
  padding: 20px;
}

.hero {
  text-align: center;
  margin-top: 60px;
}

.hero h1 {
  font-size: 2.5rem;
}
