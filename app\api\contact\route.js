import { NextResponse } from 'next/server';
import nodemailer from 'nodemailer';

// Function to verify reCAPTCHA token
async function verifyRecaptcha(token) {
  try {
    const response = await fetch('https://www.google.com/recaptcha/api/siteverify', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        secret: process.env.RECAPTCHA_SECRET_KEY || '',
        response: token,
      }),
    });

    const data = await response.json();
    return {
      success: data.success,
      score: data.score,
      action: data.action,
    };
  } catch (error) {
    console.error('reCAPTCHA verification error:', error);
    return { success: false };
  }
}

export async function POST(request) {
  try {
    const formData = await request.json();
    
    // Verify reCAPTCHA token
    const recaptchaResult = await verifyRecaptcha(formData.recaptchaToken);
    
    // Check if reCAPTCHA verification was successful and score is acceptable
    if (!recaptchaResult.success || recaptchaResult.score < 0.5) {
      return NextResponse.json(
        { success: false, message: 'reCAPTCHA validation failed. Please try again.' },
        { status: 400 }
      );
    }
    
    // Configure nodemailer with SMTP details
    const transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST || "smtp.example.com",
      port: parseInt(process.env.SMTP_PORT || "587"),
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER || "",
        pass: process.env.SMTP_PASSWORD || "",
      },
    });
    
    // Format the email content
    const emailContent = `
      <h1>New Demo Request</h1>
      <p><strong>Name:</strong> ${formData.firstName} ${formData.lastName}</p>
      <p><strong>Email:</strong> ${formData.email}</p>
      <p><strong>Phone:</strong> ${formData.phone}</p>
      <p><strong>Role:</strong> ${formData.role || 'Not specified'}</p>
      <p><strong>Country:</strong> ${formData.country || 'Not specified'}</p>
      <p><strong>Annual Revenue:</strong> ${formData.annualRevenue || 'Not specified'}</p>
      <p><strong>Number of Employees:</strong> ${formData.employees || 'Not specified'}</p>
      <p><strong>reCAPTCHA Score:</strong> ${recaptchaResult.score}</p>
    `;
    
    // Send email
    const mailOptions = {
      from: process.env.SMTP_FROM || '<EMAIL>',
      to: '<EMAIL>',
      subject: 'New Demo Request from Account-able Website',
      html: emailContent,
      replyTo: formData.email,
    };
    
    await transporter.sendMail(mailOptions);
    
    return NextResponse.json({ success: true, message: 'Email sent successfully' });
  } catch (error) {
    console.error('Error sending email:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to send email', error: error.message },
      { status: 500 }
    );
  }
} 