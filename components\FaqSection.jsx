// components/FaqSection.jsx
"use client";

import { useState } from "react";
import styles from "./FaqSection.module.css";

const faqs = [
  {
    question: "How do the sales order and GMV limit calculations work?",
    answer: (
      <>
        <p>
          Each plan (Starter, Standard, Professional, and Professional Plus)
          offers a different usage limit, which is measured by both:
        </p>
        <ul>
          <li>The number of Sales orders marked as <span className={styles.highlight}>Delivered</span></li>
          <li>The gross merchandise value (GMV) of <span className={styles.highlight}>Delivered sales orders</span></li>
        </ul>
        <p>
          Plan prices are stated in annual terms, but pricing limits are
          actually calculated based on monthly usage in equal bundles over the
          past three months. This means if your sales orders or GMV exceed the
          monthly limit for three consecutive months, your account is
          automatically upgraded to a higher plan. Usage is calculated based on
          the past calendar month.
        </p>
        <p>
          See <a href="#">this article</a> for more information and example
          calculations.
        </p>
      </>
    ),
  },
  {
    question: "What counts as a \"Delivered\" sales order?",
    answer: (
      <p>
        An order is considered "Delivered" once it has shipped and tracking
        status confirms delivery. Only delivered orders factor into your GMV
        and order-count limits.
      </p>
    ),
  },
  {
    question: "Can I pause my account if I exceed limits?",
    answer: (
      <p>
        Not at the moment—going over three consecutive months will auto-upgrade
        you. If you need custom terms, contact our sales team.
      </p>
    ),
  },
  {
    question: "How do I see my current usage?",
    answer: (
      <p>
        Head to the "Usage" tab in your dashboard, where we break down both
        order-count and GMV usage over the past 90 days.
      </p>
    ),
  },
];

export default function FaqSection() {
  // start with the first open (0) or null if you want all closed initially
  const [openIndex, setOpenIndex] = useState(0);

  // no more `: number`
  const toggle = (idx) => setOpenIndex(openIndex === idx ? null : idx);

  return (
    <section className={styles.faqSection}>
      <div className={styles.faqContainer}>
        <div className={styles.faqIntro}>
          <h2 className={styles.faqTitle}>Frequently asked questions</h2>
        </div>

        <div className={styles.faqList}>
          {faqs.map((faq, idx) => (
            <div key={idx} className={styles.faqItem}>
              <button
                className={styles.faqQuestion}
                onClick={() => toggle(idx)}
              >
                <span>{faq.question}</span>
                <span className={styles.faqIcon}>
                  {openIndex === idx ? "−" : "+"}
                </span>
              </button>
              <div 
                className={styles.faqAnswer} 
                style={{ 
                  maxHeight: openIndex === idx ? '1000px' : '0', 
                  opacity: openIndex === idx ? 1 : 0,
                  overflow: 'hidden'
                }}
              >
                {faq.answer}
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
