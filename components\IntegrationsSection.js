import styles from "../app/page.module.css";

export default function IntegrationsSection() {
  return (
    <section className={styles.integrationsSection}>
      <div className={styles.integrationsContainer}>
        <h2 className={styles.integrationsTitle}>
          account-able Integrations
        </h2>

        <div className={styles.integrationsGrid}>
          <img
            src="/logos/zoom.png"
            alt="Zoom"
            className={styles.integrationLogo}
          />
          <img
            src="/logos/calendar.png"
            alt="Google Calendar"
            className={styles.integrationLogo}
          />
          <img
            src="/logos/hubspot.png"
            alt="HubSpot"
            className={styles.integrationLogo}
          />
          <img
            src="/logos/monday.png"
            alt="monday.com"
            className={styles.integrationLogo}
          />
          <img
            src="/logos/asana.png"
            alt="Asana"
            className={styles.integrationLogo}
          />
          <img
            src="/logos/office365.png"
            alt="Office 365"
            className={styles.integrationLogo}
          />
          <img
            src="/logos/teams.png"
            alt="Microsoft Teams"
            className={styles.integrationLogo}
          />
          <img
            src="/logos/slack.png"
            alt="Slack"
            className={styles.integrationLogo}
          />
          <img
            src="/logos/drive.png"
            alt="Google Drive"
            className={styles.integrationLogo}
          />
          <img
            src="/logos/gmail.png"
            alt="Gmail"
            className={styles.integrationLogo}
          />
          <img
            src="/logos/loom.png"
            alt="Loom"
            className={styles.integrationLogo}
          />
          <img
            src="/logos/wrike.png"
            alt="Wrike"
            className={styles.integrationLogo}
          />
        </div>

        <p className={styles.integrationsFooter}>
          More Integrations Coming Soon
        </p>
      </div>
    </section>
  );
}
