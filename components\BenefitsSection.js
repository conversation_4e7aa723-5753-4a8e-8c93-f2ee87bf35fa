import styles from "../app/page.module.css";

export default function BenefitsSection() {
  return (
    <section className={styles.benefitsSection}>
      <div className={styles.benefitsContainer}>
        {/* Header */}
        <div className={styles.benefitsIntro}>
          <span className={styles.benefitsLabel}>Benefits</span>
          <h2 className={styles.benefitsTitle}>
            Get Started for Free and Start Running Better Meetings
          </h2>
        </div>

        {/* Pink vs. Green boxes */}
        <div className={styles.benefitsGrid}>
          {/* Left – Ineffective Meetings */}
          <div className={`${styles.benefitsBox} ${styles.pinkBox}`}>
            <h3 className={styles.benefitsBoxTitle}>Ineffective Meetings</h3>
            <ul className={styles.benefitsList}>
              <li className={styles.benefitsListItem}>
                <b>Meetings That Go Nowhere</b> – No purpose, no clear goals.
              </li>
              <li className={styles.benefitsListItem}>
                <b>Scattered &amp; Disorganized</b> – No structure, no set agenda.
              </li>
              <li className={styles.benefitsListItem}>
                <b>Endless Talking, No Action</b> – No follow-through, tasks get lost.
              </li>
              <li className={styles.benefitsListItem}>
                <b>Wasted Time</b> – No preparation, no accountability.
              </li>
              <li className={styles.benefitsListItem}>
                <b>Same Problems, No Progress</b> – No tracking, no improvement.
              </li>
              <li className={styles.benefitsListItem}>
                <b>People Check Out</b> – No engagement, no reason to pay attention.
              </li>
            </ul>
          </div>

          {/* Right – account-able Meetings */}
          <div className={`${styles.benefitsBox} ${styles.greenBox}`}>
            <h3 className={styles.benefitsBoxTitle}>account-able Meetings</h3>
            <ul className={styles.benefitsList}>
              <li className={styles.benefitsListItem}>
                <b>Clear Agendas &amp; Templates</b> – Meetings start with purpose and focus.
              </li>
              <li className={styles.benefitsListItem}>
                <b>Action-Powered Discussions</b> – Every decision turns into a next step.
              </li>
              <li className={styles.benefitsListItem}>
                <b>Stay On Track With Smart Timers</b> – No more meetings that drag on.
              </li>
              <li className={styles.benefitsListItem}>
                <b>Progress That&apos;s Measurable</b> – KPIs and performance tracking keep everyone accountable.
              </li>
              <li className={styles.benefitsListItem}>
                <b>Automated Pre-Caps &amp; Recaps</b> – Know what&apos;s coming, and never forget what was decided.
              </li>
              <li className={styles.benefitsListItem}>
                <b>AI &amp; Recording Integrations</b> – Capture every detail without lifting a finger.
              </li>
              <li className={styles.benefitsListItem}>
                <b>Meeting Ratings</b> – Feedback + transparency for every meeting.
              </li>
              <li className={styles.benefitsListItem}>
                <b>Integrations with Systems You Love</b> – Connect with project management, CRM, and more.
              </li>
            </ul>
          </div>
        </div>

        {/* Bottom CTA */}
        <div className={styles.ctaBox}>
          <h3 className={styles.ctaTitle}>Use account-able for FREE</h3>
          <p className={styles.ctaText}>
            We are excited to extend free invites to beta users looking to run better meetings. This offer won&apos;t last long!
          </p>
          <a href="/contact" className={styles.ctaButton}>Get a Demo</a>
        </div>
      </div>
    </section>
  );
}
