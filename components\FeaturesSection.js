import styles from "../app/page.module.css";

export default function FeaturesSection() {
  return (
    <section className={styles.featuresSection}>
      <div className={styles.featuresContainer}>
        <div className={styles.featuresIntro}>
          <p className={styles.featuresLabel}>Who is account-able for?</p>
          <h2 className={styles.featuresTitle}>
            Simple, structured meetings for coaches, companies, and communities
          </h2>
          <p className={styles.featuresSubtitle}>
            No more meeting chaos. Whether you&apos;re coaching a team, running a
            business, or leading a community, account-able keeps every meeting
            organized, action-driven, and easy to follow—so that every
            conversation counts.
          </p>
        </div>

        <div className={styles.featuresGrid}>
          <div className={styles.featureCard}>
            <div className={styles.featureImageContainer}>
              <img
                className={styles.featureImage}
                src="/images/coaches.webp"
                alt="Coaches + Consultants"
              />
              <img
                className={styles.featureImageOverlay}
                src="/images/coaches2.png"
                alt="Coaches + Consultants overlay"
              />
            </div>
            <div className={styles.featureContent}>
              <h3 className={styles.featureCardTitle}>
                Coaches + Consultants
              </h3>
              <p className={styles.featureCardText}>
                Client sessions shouldn&apos;t feel like casual check-ins. account-able
                gives you a system to structure engagements, track key takeaways,
                and reinforce accountability—so your clients actually follow
                through.
              </p>
            </div>
          </div>

          <div className={styles.featureCard}>
            <div className={styles.featureImageContainer}>
              <img
                className={styles.featureImage}
                src="/images/companies.webp"
                alt="Companies"
              />
              <img
                className={styles.featureImageOverlay}
                src="/images/companies2.png"
                alt="Companies overlay"
              />
            </div>
            <div className={styles.featureContent}>
              <h3 className={styles.featureCardTitle}>Companies</h3>
              <p className={styles.featureCardText}>
                Meetings should lead to action, not just conversation. Our
                platform helps teams stay aligned, document key decisions, and
                ensure that action items don&apos;t get lost—whether it&apos;s a client
                call, a leadership meeting, or a 1-on-1.
              </p>
            </div>
          </div>

          <div className={styles.featureCard}>
            <div className={styles.featureImageContainer}>
              <img
                className={styles.featureImage}
                src="/images/communities.webp"
                alt="Communities"
              />
              <img
                className={styles.featureImageOverlay}
                src="/images/communities2.png"
                alt="Communities overlay"
              />
            </div>
            <div className={styles.featureContent}>
              <h3 className={styles.featureCardTitle}>Communities</h3>
              <p className={styles.featureCardText}>
                Leading a community requires clarity and organization.
                Non-profits, mastermind groups, neighborhood committees, and more
                centralize their hubs to track discussions, review minutes, and
                drive real change.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
